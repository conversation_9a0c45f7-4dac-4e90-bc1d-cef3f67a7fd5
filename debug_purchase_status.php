<?php
// 调试购买状态的脚本

// 数据库配置
$host = '127.0.0.1';
$dbname = 'houtai';
$username = 'root';
$password = '661381abc';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $postId = 5;
    $userId = 2;
    $segmentIndex = 2;
    
    echo "=== 购买状态调试 ===\n";
    echo "帖子ID: {$postId}\n";
    echo "用户ID: {$userId}\n";
    echo "段落索引: {$segmentIndex}\n\n";
    
    // 检查购买记录
    echo "=== 购买记录查询 ===\n";
    $stmt = $pdo->prepare("SELECT * FROM fa_post_purchases WHERE user_id = ? AND post_id = ?");
    $stmt->execute([$userId, $postId]);
    $purchases = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($purchases) {
        echo "找到购买记录:\n";
        foreach ($purchases as $purchase) {
            echo "  ID: {$purchase['id']}\n";
            echo "  用户ID: {$purchase['user_id']}\n";
            echo "  帖子ID: {$purchase['post_id']}\n";
            echo "  段落索引: {$purchase['segment_index']}\n";
            echo "  价格: {$purchase['price']}\n";
            echo "  状态: {$purchase['status']}\n";
            echo "  购买时间: " . date('Y-m-d H:i:s', $purchase['createtime']) . "\n";
            echo "  ---\n";
        }
    } else {
        echo "❌ 没有找到购买记录\n";
    }
    
    // 检查特定段落的购买状态
    echo "\n=== 段落购买状态检查 ===\n";
    $stmt = $pdo->prepare("SELECT * FROM fa_post_purchases WHERE user_id = ? AND post_id = ? AND segment_index = ? AND status = 1");
    $stmt->execute([$userId, $postId, $segmentIndex]);
    $segmentPurchase = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($segmentPurchase) {
        echo "✅ 用户{$userId}已购买帖子{$postId}的段落{$segmentIndex}\n";
        echo "购买记录ID: {$segmentPurchase['id']}\n";
        echo "购买时间: " . date('Y-m-d H:i:s', $segmentPurchase['createtime']) . "\n";
    } else {
        echo "❌ 用户{$userId}未购买帖子{$postId}的段落{$segmentIndex}\n";
        
        // 检查是否有旧的购买记录（segment_index=0）
        $stmt = $pdo->prepare("SELECT * FROM fa_post_purchases WHERE user_id = ? AND post_id = ? AND segment_index = 0 AND status = 1");
        $stmt->execute([$userId, $postId]);
        $oldPurchase = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($oldPurchase) {
            echo "⚠️  发现旧的购买记录（segment_index=0），需要更新\n";
            echo "旧记录ID: {$oldPurchase['id']}\n";
            
            // 更新旧记录
            $stmt = $pdo->prepare("UPDATE fa_post_purchases SET segment_index = ? WHERE id = ?");
            $result = $stmt->execute([$segmentIndex, $oldPurchase['id']]);
            
            if ($result) {
                echo "✅ 已更新旧购买记录的段落索引\n";
            } else {
                echo "❌ 更新失败\n";
            }
        }
    }
    
    // 检查帖子配置
    echo "\n=== 帖子配置检查 ===\n";
    $stmt = $pdo->prepare("SELECT paid_content_index, paid_content_price FROM fa_posts WHERE id = ?");
    $stmt->execute([$postId]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "收费段落索引: {$post['paid_content_index']}\n";
    echo "收费价格: {$post['paid_content_price']}\n";
    
} catch (PDOException $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}
