-- 修复数据库和收费段落索引

-- 1. 添加多段落购买支持字段（如果不存在）
ALTER TABLE `fa_posts` ADD COLUMN IF NOT EXISTS `paid_segments_config` TEXT COMMENT '收费段落配置JSON';
ALTER TABLE `fa_post_purchases` ADD COLUMN IF NOT EXISTS `segment_index` int(10) DEFAULT 0 COMMENT '购买的段落索引(0表示整个帖子)';

-- 2. 修复帖子ID为5的收费段落索引（从7改为2）
UPDATE `fa_posts` SET `paid_content_index` = 2 WHERE `id` = 5;

-- 3. 更新现有购买记录的段落索引
UPDATE `fa_post_purchases` pp 
JOIN `fa_posts` p ON pp.post_id = p.id 
SET pp.segment_index = p.paid_content_index 
WHERE pp.segment_index = 0 AND p.paid_content_index > 0;

-- 4. 添加索引（如果不存在）
ALTER TABLE `fa_post_purchases` ADD INDEX IF NOT EXISTS `idx_user_post_segment` (`user_id`, `post_id`, `segment_index`);

-- 5. 查看修复结果
SELECT 
    id, 
    title, 
    paid_content_index, 
    paid_content_price, 
    paid_segments_config 
FROM `fa_posts` 
WHERE id = 5;

SELECT '✅ 数据库修复完成！' as '执行结果';
