<?php
// 测试新的收费逻辑

// 数据库配置
$host = '127.0.0.1';
$dbname = 'houtai';
$username = 'root';
$password = '661381abc';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 测试新的收费逻辑 ===\n\n";
    
    // 测试内容
    $testContent = "免费内容1[SPLIT]收费内容1[SPLIT]免费内容2[SPLIT]收费内容2[SPLIT]免费内容3";
    
    echo "测试内容: {$testContent}\n\n";
    
    // 分割内容
    $segments = explode('[SPLIT]', $testContent);
    
    echo "=== 段落分析 ===\n";
    foreach ($segments as $index => $segment) {
        if (trim($segment) === '') {
            continue;
        }
        
        $segmentIndex = $index + 1;
        $isPaidSegment = ($index % 2 === 1); // 偶数位置是收费内容
        $type = $isPaidSegment ? '收费' : '免费';
        
        echo "段落 {$segmentIndex} (索引{$index}): [{$type}] '{$segment}'\n";
    }
    
    echo "\n=== 显示逻辑测试 ===\n";
    
    // 模拟未购买用户
    echo "未购买用户看到的内容:\n";
    $displaySegments = [];
    foreach ($segments as $index => $segment) {
        if (trim($segment) === '') {
            continue;
        }
        
        $isPaidSegment = ($index % 2 === 1);
        
        if ($isPaidSegment) {
            echo "  跳过收费段落: '{$segment}'\n";
            continue;
        } else {
            echo "  显示免费段落: '{$segment}'\n";
            $displaySegments[] = $segment;
        }
    }
    
    $finalContent = implode('', $displaySegments);
    echo "最终显示内容: '{$finalContent}'\n\n";
    
    // 模拟已购买用户
    echo "已购买用户看到的内容:\n";
    $displaySegments = [];
    foreach ($segments as $index => $segment) {
        if (trim($segment) === '') {
            continue;
        }
        
        $isPaidSegment = ($index % 2 === 1);
        
        if ($isPaidSegment) {
            echo "  显示已购买收费段落: '{$segment}'\n";
        } else {
            echo "  显示免费段落: '{$segment}'\n";
        }
        $displaySegments[] = $segment;
    }
    
    $finalContent = implode('', $displaySegments);
    echo "最终显示内容: '{$finalContent}'\n\n";
    
    echo "=== 收费段落信息 ===\n";
    $paidSegments = [];
    foreach ($segments as $index => $segment) {
        if (trim($segment) === '') {
            continue;
        }
        
        $segmentIndex = $index + 1;
        $isPaidSegment = ($index % 2 === 1);
        
        if ($isPaidSegment) {
            $paidSegments[] = [
                'segment_index' => $segmentIndex,
                'price' => 100,
                'title' => '收费内容 ' . (count($paidSegments) + 1),
                'has_purchased' => false,
                'preview' => mb_substr(strip_tags($segment), 0, 20) . '...'
            ];
        }
    }
    
    echo "收费段落列表:\n";
    foreach ($paidSegments as $segment) {
        echo "  段落{$segment['segment_index']}: {$segment['title']} - {$segment['price']}金币 - 预览: {$segment['preview']}\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}
