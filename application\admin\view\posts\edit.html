<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">标题:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" data-rule="required">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">分类:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-category_id" class="form-control selectpicker" name="row[category_id]" data-rule="required">
                <option value="">请选择分类</option>
                {volist name="categoryList" id="category"}
                <option value="{$category.id}" {if condition="$category.id == $row.category_id"}selected{/if}>{$category.name}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">帖子类型:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-post_type" class="form-control selectpicker" name="row[post_type]" data-live-search="false">
                {volist name="postTypes" id="typeName" key="typeKey"}
                <option value="{$typeKey}" {if condition="$typeKey == $row.post_type"}selected{/if}>{$typeName}</option>
                {/volist}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">图标设置:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[icon_type]" value="category" {if condition="$row.icon_type == 'category' || !$row.icon_type"}checked{/if}> 使用分类图标</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[icon_type]" value="upload" {if condition="$row.icon_type == 'upload'"}checked{/if}> 上传自定义图标</label>
            </div>
        </div>
    </div>
    <div class="form-group" id="category-icon-info" {if condition="$row.icon_type == 'upload'"}style="display: none;"{/if}>
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> 帖子将自动使用所选分类的图标
            </div>
        </div>
    </div>
    <div class="form-group" id="upload-icon" {if condition="$row.icon_type != 'upload'"}style="display: none;"{/if}>
        <label class="control-label col-xs-12 col-sm-2">上传自定义图标:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-icon_path" class="form-control" size="50" name="row[icon_path]" type="text" value="{$row.icon_path|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-icon" class="btn btn-danger plupload" data-input-id="c-icon_path" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-icon"><i class="fa fa-upload"></i> 上传</button></span>
                </div>
                <span class="msg-box n-right" for="c-icon_path"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-icon"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-content" class="form-control editor" rows="5" name="row[content]" cols="50" data-rule="required">{$row.content|htmlentities}</textarea>
            <div class="help-block">
                <small class="text-muted">💡 <strong>收费内容设置：</strong>使用 <code>[SPLIT]</code> 分隔内容段落，偶数段落（第2、4、6段...）自动设为收费内容</small>
                <br>
                <small class="text-muted">📝 <strong>示例：</strong>免费内容1<code>[SPLIT]</code>收费内容1<code>[SPLIT]</code>免费内容2<code>[SPLIT]</code>收费内容2<code>[SPLIT]</code>免费内容3</small>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">收费价格:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input type="number" id="c-paid_content_price" name="row[paid_content_price]" class="form-control" placeholder="所有收费段落的价格" min="0" value="{$row.paid_content_price|default=100}">
                <span class="input-group-addon">金币</span>
            </div>
            <div class="help-block">
                <small class="text-muted">设置所有收费段落的统一价格（用户需要为每个收费段落单独购买）</small>
            </div>
            <!-- 隐藏字段，保持兼容性 -->
            <input type="hidden" name="row[paid_content_index]" value="{$row.paid_content_index|default=1}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">置顶:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[is_top]" value="1" {if condition="$row.is_top == 1"}checked{/if}> 是</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[is_top]" value="0" {if condition="$row.is_top == 0"}checked{/if}> 否</label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">热门:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[is_hot]" value="1" {if condition="$row.is_hot == 1"}checked{/if}> 是</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[is_hot]" value="0" {if condition="$row.is_hot == 0"}checked{/if}> 否</label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">状态:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label><input type="radio" name="row[status]" value="published" {if condition="$row.status == 'published'"}checked{/if}> 已发布</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[status]" value="draft" {if condition="$row.status == 'draft'"}checked{/if}> 草稿</label>
            </div>
            <div class="radio">
                <label><input type="radio" name="row[status]" value="hidden" {if condition="$row.status == 'hidden'"}checked{/if}> 隐藏</label>
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">确定</button>
            <button type="reset" class="btn btn-default btn-embossed">重置</button>
        </div>
    </div>
</form>

<style>
.icon-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.icon-item {
    text-align: center;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    min-width: 80px;
}
.icon-item:hover {
    border-color: #007bff;
}
.icon-item.selected {
    border-color: #007bff;
    background-color: #f0f8ff;
}
</style>

<script>
$(function() {
    // 图标类型切换
    $('input[name="row[icon_type]"]').change(function() {
        if ($(this).val() === 'category') {
            $('#category-icon-info').show();
            $('#upload-icon').hide();
        } else {
            $('#category-icon-info').hide();
            $('#upload-icon').show();
        }
    });

    // 收费内容价格验证
    $('#c-paid_content_price').on('input', function() {
        var price = parseInt($(this).val());
        if (price < 0) {
            $(this).val(0);
        }
    });

    // 初始化状态
    $('#c-paid_content_index').trigger('change');

    // 确保selectpicker正确显示选中值
    setTimeout(function() {
        // 强制设置选中值
        $('#c-post_type').val('{$row.post_type}');
        $('#c-post_type').selectpicker('refresh');

        // 如果还是不对，尝试手动设置
        if ($('#c-post_type').val() !== '{$row.post_type}') {
            $('#c-post_type option').removeAttr('selected');
            $('#c-post_type option[value="{$row.post_type}"]').attr('selected', 'selected');
            $('#c-post_type').selectpicker('refresh');
        }
    }, 200);
});
</script>
