-- 修复购买记录的段落索引

-- 1. 查看修复前的记录
SELECT 
    id,
    user_id,
    post_id,
    segment_index,
    price,
    status,
    FROM_UNIXTIME(createtime) as purchase_time
FROM fa_post_purchases 
WHERE id = 11;

-- 2. 将购买记录ID为11的segment_index从0改为2
UPDATE fa_post_purchases 
SET segment_index = 2 
WHERE id = 11;

-- 3. 验证修复结果
SELECT 
    id,
    user_id,
    post_id,
    segment_index,
    price,
    status,
    FROM_UNIXTIME(createtime) as purchase_time
FROM fa_post_purchases 
WHERE id = 11;

-- 4. 确认用户2现在可以看到帖子5的段落2
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 修复成功：用户2已购买帖子5的段落2'
        ELSE '❌ 修复失败：用户2未购买帖子5的段落2'
    END as fix_result
FROM fa_post_purchases 
WHERE user_id = 2 AND post_id = 5 AND segment_index = 2 AND status = 1;
