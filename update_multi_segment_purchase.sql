-- =====================================================
-- 多段落购买功能数据库更新
-- =====================================================

-- 1. 给帖子表添加多段落收费配置字段
ALTER TABLE `fa_posts` ADD COLUMN `paid_segments_config` TEXT COMMENT '收费段落配置JSON';

-- 2. 给购买记录表添加段落索引字段
ALTER TABLE `fa_post_purchases` ADD COLUMN `segment_index` int(10) DEFAULT 0 COMMENT '购买的段落索引(0表示整个帖子)';

-- 3. 更新现有购买记录，设置segment_index为对应的paid_content_index
UPDATE `fa_post_purchases` pp 
JOIN `fa_posts` p ON pp.post_id = p.id 
SET pp.segment_index = p.paid_content_index 
WHERE pp.segment_index = 0 AND p.paid_content_index > 0;

-- 4. 添加新的索引
ALTER TABLE `fa_post_purchases` ADD INDEX `idx_user_post_segment` (`user_id`, `post_id`, `segment_index`);

-- 执行完成提示
SELECT '✅ 多段落购买功能数据库更新完成！' as '执行结果';
SELECT '📋 已添加paid_segments_config字段到fa_posts表' as '帖子表更新';
SELECT '📋 已添加segment_index字段到fa_post_purchases表' as '购买记录表更新';
SELECT '📋 已更新现有购买记录的段落索引' as '数据迁移';
