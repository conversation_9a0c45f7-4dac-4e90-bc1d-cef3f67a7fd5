<?php
// 检查数据库表是否存在的脚本

require_once 'thinkphp/start.php';

use think\Db;

try {
    // 检查 fa_post_purchases 表是否存在
    $tables = Db::query("SHOW TABLES LIKE 'fa_post_purchases'");
    
    if (empty($tables)) {
        echo "❌ 表 fa_post_purchases 不存在，需要创建\n";
        echo "请执行以下SQL语句：\n\n";
        
        $sql = "CREATE TABLE `fa_post_purchases` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '用户ID',
  `post_id` int(10) unsigned NOT NULL COMMENT '帖子ID',
  `price` int(10) NOT NULL COMMENT '支付积分',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1已支付',
  `createtime` int(10) DEFAULT NULL COMMENT '购买时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_post` (`user_id`,`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='帖子购买记录表';";
        
        echo $sql . "\n\n";
        
        // 尝试自动创建表
        try {
            Db::execute($sql);
            echo "✅ 表 fa_post_purchases 创建成功！\n";
        } catch (Exception $e) {
            echo "❌ 自动创建表失败：" . $e->getMessage() . "\n";
            echo "请手动执行上面的SQL语句\n";
        }
    } else {
        echo "✅ 表 fa_post_purchases 已存在\n";
    }
    
    // 检查 fa_posts 表的字段
    $columns = Db::query("SHOW COLUMNS FROM fa_posts LIKE 'paid_content_index'");
    if (empty($columns)) {
        echo "❌ fa_posts 表缺少 paid_content_index 字段\n";
        echo "请执行：ALTER TABLE `fa_posts` ADD COLUMN `paid_content_index` int(10) DEFAULT 0 COMMENT '收费内容段落索引(从1开始,0表示无收费内容)';\n";
    } else {
        echo "✅ fa_posts 表的 paid_content_index 字段已存在\n";
    }
    
    $columns = Db::query("SHOW COLUMNS FROM fa_posts LIKE 'paid_content_price'");
    if (empty($columns)) {
        echo "❌ fa_posts 表缺少 paid_content_price 字段\n";
        echo "请执行：ALTER TABLE `fa_posts` ADD COLUMN `paid_content_price` int(10) DEFAULT 0 COMMENT '收费内容价格(积分)';\n";
    } else {
        echo "✅ fa_posts 表的 paid_content_price 字段已存在\n";
    }
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败：" . $e->getMessage() . "\n";
}
