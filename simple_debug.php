<?php
// 简单的数据库调试脚本

// 数据库配置
$host = '127.0.0.1';
$dbname = 'houtai';
$username = 'root';
$password = '661381abc';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 数据库连接成功 ===\n";
    
    // 查询帖子ID为5的数据
    $stmt = $pdo->prepare("SELECT * FROM fa_posts WHERE id = 5");
    $stmt->execute();
    $post = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($post) {
        echo "=== 帖子信息 ===\n";
        echo "ID: " . $post['id'] . "\n";
        echo "标题: " . $post['title'] . "\n";
        echo "paid_content_index: " . ($post['paid_content_index'] ?? 'NULL') . "\n";
        echo "paid_content_price: " . ($post['paid_content_price'] ?? 'NULL') . "\n";
        echo "paid_segments_config: " . ($post['paid_segments_config'] ?? 'NULL') . "\n";
        echo "内容: " . $post['content'] . "\n\n";
        
        // 分析内容
        $content = $post['content'];
        $segments = explode('[SPLIT]', $content);
        
        echo "=== 内容分析 ===\n";
        echo "分割后的段落数: " . count($segments) . "\n";
        
        for ($i = 0; $i < count($segments); $i++) {
            echo "段落 " . ($i + 1) . ": '" . trim($segments[$i]) . "'\n";
        }
        
        // 检查收费配置
        $paidIndex = $post['paid_content_index'];
        $paidPrice = $post['paid_content_price'];
        
        echo "\n=== 收费配置 ===\n";
        if ($paidIndex > 0 && $paidPrice > 0) {
            echo "收费段落: 第{$paidIndex}段\n";
            echo "收费价格: {$paidPrice}金币\n";
            
            if ($paidIndex <= count($segments)) {
                echo "收费内容: '" . trim($segments[$paidIndex - 1]) . "'\n";
            } else {
                echo "❌ 收费段落索引超出范围！\n";
            }
        } else {
            echo "❌ 没有设置收费内容\n";
        }
        
    } else {
        echo "❌ 帖子ID为5的记录不存在\n";
    }
    
    // 检查购买记录
    echo "\n=== 购买记录 ===\n";
    $stmt = $pdo->prepare("SELECT * FROM fa_post_purchases WHERE post_id = 5");
    $stmt->execute();
    $purchases = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($purchases) {
        foreach ($purchases as $purchase) {
            echo "用户ID: {$purchase['user_id']}, 段落: {$purchase['segment_index']}, 价格: {$purchase['price']}, 时间: " . date('Y-m-d H:i:s', $purchase['createtime']) . "\n";
        }
    } else {
        echo "没有购买记录\n";
    }
    
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}
