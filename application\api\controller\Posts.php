<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Post;
use app\common\model\PostCategory;
use think\Validate;

/**
 * 帖子接口
 */
class Posts extends Api
{
    protected $noNeedLogin = ['index', 'detail', 'categories'];
    protected $noNeedRight = '*';

    /**
     * 帖子列表
     *
     * @ApiMethod (GET)
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="limit", type="integer", required=false, description="每页数量")
     * @ApiParams (name="category_id", type="integer", required=false, description="分类ID")
     * @ApiParams (name="post_type", type="string", required=false, description="帖子类型")
     * @ApiParams (name="keyword", type="string", required=false, description="搜索关键词")
     */
    public function index()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $categoryId = $this->request->get('category_id');
        $postType = $this->request->get('post_type');
        $keyword = $this->request->get('keyword');

        $where = ['status' => 'published'];
        
        if ($categoryId) {
            $where['category_id'] = $categoryId;
        }
        
        if ($postType) {
            $where['post_type'] = $postType;
        }

        $query = Post::with(['user', 'category'])->where($where);
        
        if ($keyword) {
            $query->where('title', 'like', '%' . $keyword . '%');
        }

        $list = $query->order('is_top', 'desc')
                     ->order('createtime', 'desc')
                     ->paginate($limit, false, ['page' => $page]);

        foreach ($list as $item) {
            $item->visible(['id', 'title', 'post_type', 'post_type_text', 'icon_url', 'views', 'replies', 'likes', 'is_top', 'is_hot', 'paid_content_index', 'paid_content_price', 'createtime']);
            $item->visible(['user']);
            $item->getRelation('user')->visible(['id', 'username', 'nickname', 'avatar']);
            $item->visible(['category']);
            $item->getRelation('category')->visible(['id', 'name']);
        }

        $this->success('获取成功', [
            'list' => $list->items(),
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 帖子详情
     *
     * @ApiMethod (GET)
     * @ApiParams (name="id", type="integer", required=true, description="帖子ID")
     */
    public function detail($id = null)
    {
        // 支持两种方式获取ID：路径参数和GET参数
        if (!$id) {
            $id = $this->request->get('id');
        }
        if (!$id) {
            $this->error('参数错误');
        }

        $post = Post::with(['user', 'category'])
                   ->where('id', $id)
                   ->where('status', 'published')
                   ->find();

        if (!$post) {
            $this->error('帖子不存在');
        }

        // 增加浏览次数
        $post->incrementViews();

        // 获取当前用户ID（如果已登录）
        $userId = null;
        if ($this->auth && $this->auth->isLogin()) {
            $userId = $this->auth->id;
        }

        // 处理收费内容显示
        $displayContent = $post->getDisplayContent($userId);

        $post->visible(['id', 'title', 'post_type', 'post_type_text', 'icon_url', 'views', 'replies', 'likes', 'is_top', 'is_hot', 'createtime']);
        $post->visible(['user']);
        $post->getRelation('user')->visible(['id', 'username', 'nickname', 'avatar']);
        $post->visible(['category']);
        $post->getRelation('category')->visible(['id', 'name']);

        // 准备返回数据
        $result = $post->toArray();
        $result['content'] = $displayContent;

        // 如果有收费内容，检查是否需要显示购买信息
        if ($post->hasPaidContent()) {
            $hasPurchased = false;
            if ($userId) {
                $hasPurchased = \app\common\model\PostPurchase::hasPurchased($userId, $post->id);
            }

            // 如果未购买（包括未登录用户），显示购买信息
            if (!$hasPurchased) {
                $result['purchase_info'] = [
                    'price' => $post->paid_content_price,
                    'purchase_count' => \app\common\model\PostPurchase::getPurchaseCount($post->id),
                    'post_id' => $post->id
                ];
            }
        }

        $this->success('获取成功', $result);
    }

    /**
     * 发布帖子
     *
     * @ApiMethod (POST)
     * @ApiParams (name="title", type="string", required=true, description="帖子标题")
     * @ApiParams (name="content", type="string", required=true, description="帖子内容")
     * @ApiParams (name="category_id", type="integer", required=true, description="分类ID")
     * @ApiParams (name="post_type", type="string", required=false, description="帖子类型")
     * @ApiParams (name="icon_type", type="string", required=false, description="图标类型")
     * @ApiParams (name="icon_path", type="string", required=false, description="图标路径")
     */
    public function create()
    {
        $title = $this->request->post('title');
        $content = $this->request->post('content');
        $categoryId = $this->request->post('category_id');
        $postType = $this->request->post('post_type', 'normal');
        $iconType = $this->request->post('icon_type', 'builtin');
        $iconPath = $this->request->post('icon_path');

        // 验证参数
        $validate = Validate::make([
            'title' => 'require|length:1,255',
            'content' => 'require',
            'category_id' => 'require|integer',
            'post_type' => 'in:normal,sale,hot,expert'
        ]);

        if (!$validate->check([
            'title' => $title,
            'content' => $content,
            'category_id' => $categoryId,
            'post_type' => $postType
        ])) {
            $this->error($validate->getError());
        }

        // 验证分类是否存在
        $category = PostCategory::where('id', $categoryId)->where('status', 1)->find();
        if (!$category) {
            $this->error('分类不存在');
        }

        $data = [
            'user_id' => $this->auth->id,
            'title' => $title,
            'content' => $content,
            'category_id' => $categoryId,
            'post_type' => $postType,
            'icon_type' => $iconType,
            'icon_path' => $iconPath,
            'status' => 'published'
        ];

        $post = Post::create($data);
        if ($post) {
            $this->success('发布成功', ['id' => $post->id]);
        } else {
            $this->error('发布失败');
        }
    }

    /**
     * 获取分类列表
     *
     * @ApiMethod (GET)
     */
    public function categories()
    {
        $categories = PostCategory::getEnabledCategories();
        $this->success('获取成功', $categories);
    }

    /**
     * 购买收费内容
     *
     * @ApiMethod (POST)
     * @ApiParams (name="post_id", type="integer", required=true, description="帖子ID")
     * @ApiParams (name="price", type="integer", required=true, description="支付价格")
     */
    public function purchase()
    {
        try {
            // 检查登录状态
            if (!$this->auth->isLogin()) {
                $this->error('请先登录');
            }

            $postId = $this->request->post('post_id');
            $price = $this->request->post('price');

            \think\Log::info('购买请求参数：postId=' . $postId . ', price=' . $price . ', userId=' . $this->auth->id);

            if (!$postId || !$price) {
                $this->error('参数错误：缺少帖子ID或价格');
            }

            // 验证帖子是否存在
            $post = Post::where('id', $postId)->where('status', 'published')->find();
            if (!$post) {
                $this->error('帖子不存在');
            }

            \think\Log::info('帖子信息：' . json_encode($post->toArray()));

            // 检查帖子是否有收费内容
            if (!$post->hasPaidContent()) {
                $this->error('该帖子没有收费内容');
            }

            // 验证价格是否正确
            if ($post->paid_content_price != $price) {
                $this->error('价格不匹配，帖子价格：' . $post->paid_content_price . '，传入价格：' . $price);
            }

            // 检查是否已经购买过
            if (\app\common\model\PostPurchase::hasPurchased($this->auth->id, $postId)) {
                $this->error('您已经购买过此内容');
            }

            // 检查用户积分是否足够
            $user = $this->auth->getUser();
            \think\Log::info('用户积分：' . $user->score . '，需要积分：' . $price);

            if ($user->score < $price) {
                $this->error('金币不足，当前金币：' . $user->score);
            }
        } catch (\Exception $e) {
            \think\Log::error('购买验证失败：' . $e->getMessage() . ' 文件：' . $e->getFile() . ' 行号：' . $e->getLine());
            $this->error('购买验证失败：' . $e->getMessage());
        }

        // 开始事务
        \think\Db::startTrans();
        try {
            // 使用User模型的score方法扣除积分（会自动记录积分日志）
            \app\common\model\User::score(-$price, $this->auth->id, '购买帖子收费内容：' . $post->title);

            // 创建购买记录
            \app\common\model\PostPurchase::create([
                'user_id' => $this->auth->id,
                'post_id' => $postId,
                'price' => $price,
                'status' => 1
            ]);

            \think\Db::commit();
            $this->success('购买成功');
        } catch (\Exception $e) {
            \think\Db::rollback();
            // 记录详细错误信息到日志
            \think\Log::error('购买失败：' . $e->getMessage() . ' 文件：' . $e->getFile() . ' 行号：' . $e->getLine());
            $this->error('购买失败：' . $e->getMessage());
        }
    }

    /**
     * 上传图标
     *
     * @ApiMethod (POST)
     */
    public function uploadIcon()
    {
        $file = $this->request->file('file');
        if (!$file) {
            $this->error('请选择文件');
        }

        // 验证文件
        $validate = [
            'size' => 2097152, // 2MB
            'ext' => 'jpg,jpeg,png,gif'
        ];

        $info = $file->validate($validate)->move(ROOT_PATH . 'public/uploads/icons');
        if ($info) {
            $filePath = '/uploads/icons/' . $info->getSaveName();
            $this->success('上传成功', ['url' => $filePath]);
        } else {
            $this->error($file->getError());
        }
    }

    /**
     * 获取内置图标列表
     *
     * @ApiMethod (GET)
     */
    public function builtinIcons()
    {
        $icons = Post::getBuiltinIcons();
        // 转换为前端需要的格式
        $result = [];
        foreach ($icons as $key => $info) {
            $result[$key] = $info['path'];
        }
        $this->success('获取成功', $result);
    }
}
