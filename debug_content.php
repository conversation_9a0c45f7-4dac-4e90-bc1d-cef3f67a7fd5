<?php
// 调试内容显示的脚本

require_once 'thinkphp/start.php';

use app\common\model\Post;
use app\common\model\PostPurchase;

try {
    $postId = 5;
    $userId = 2;
    
    // 获取帖子
    $post = Post::where('id', $postId)->find();
    
    if ($post) {
        echo "=== 帖子信息 ===\n";
        echo "ID: " . $post->id . "\n";
        echo "标题: " . $post->title . "\n";
        echo "paid_content_index: " . $post->paid_content_index . "\n";
        echo "paid_content_price: " . $post->paid_content_price . "\n";
        echo "hasPaidContent(): " . ($post->hasPaidContent() ? 'true' : 'false') . "\n";
        
        echo "\n=== 购买状态检查 ===\n";
        $hasPurchased = PostPurchase::hasPurchased($userId, $postId);
        echo "用户{$userId}是否购买了帖子{$postId}: " . ($hasPurchased ? 'true' : 'false') . "\n";
        
        echo "\n=== 内容分割测试 ===\n";
        $content = $post->content;
        $contents = explode('[SPLIT]', $content);
        echo "内容段落数: " . count($contents) . "\n";
        for ($i = 0; $i < count($contents); $i++) {
            echo "段落" . ($i + 1) . " (长度: " . strlen($contents[$i]) . "): " . substr(strip_tags($contents[$i]), 0, 50) . "...\n";
        }
        
        echo "\n=== getDisplayContent测试 ===\n";
        echo "未登录用户看到的内容:\n";
        $contentForGuest = $post->getDisplayContent(null);
        echo "长度: " . strlen($contentForGuest) . "\n";
        echo "前100字符: " . substr(strip_tags($contentForGuest), 0, 100) . "...\n";
        
        echo "\n已购买用户看到的内容:\n";
        $contentForPurchased = $post->getDisplayContent($userId);
        echo "长度: " . strlen($contentForPurchased) . "\n";
        echo "前100字符: " . substr(strip_tags($contentForPurchased), 0, 100) . "...\n";
        
        echo "\n=== 对比 ===\n";
        echo "内容长度是否不同: " . (strlen($contentForGuest) != strlen($contentForPurchased) ? 'true' : 'false') . "\n";
        
    } else {
        echo "帖子不存在\n";
    }
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
    echo "堆栈：" . $e->getTraceAsString() . "\n";
}
