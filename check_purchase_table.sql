-- 检查购买记录表的结构和数据

-- 1. 查看表结构
DESCRIBE fa_post_purchases;

-- 2. 查看所有购买记录
SELECT * FROM fa_post_purchases;

-- 3. 查看帖子ID为5的购买记录
SELECT * FROM fa_post_purchases WHERE post_id = 5;

-- 4. 查看用户ID为2的购买记录
SELECT * FROM fa_post_purchases WHERE user_id = 2;

-- 5. 检查是否有segment_index字段
SHOW COLUMNS FROM fa_post_purchases LIKE 'segment_index';

-- 6. 如果没有segment_index字段，添加它
-- ALTER TABLE fa_post_purchases ADD COLUMN segment_index int(10) DEFAULT 0 COMMENT '购买的段落索引(0表示整个帖子)';

-- 7. 更新现有购买记录的segment_index
-- UPDATE fa_post_purchases pp 
-- JOIN fa_posts p ON pp.post_id = p.id 
-- SET pp.segment_index = p.paid_content_index 
-- WHERE pp.segment_index = 0 AND p.paid_content_index > 0;
