<?php

namespace app\common\model;

use think\Model;

/**
 * 帖子模型
 */
class Post extends Model
{

    // 表名
    protected $name = 'posts';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'post_type_text',
        'status_text',
        'icon_url',
        'display_content'
    ];

    // 帖子类型
    const POST_TYPES = [
        'normal' => '普通帖',
        'sale' => '出售帖',
        'hot' => '热门帖',
        'expert' => '高手帖'
    ];

    // 帖子状态
    const STATUS_LIST = [
        'draft' => '草稿',
        'published' => '已发布',
        'hidden' => '隐藏',
        'deleted' => '已删除'
    ];

    /**
     * 获取内置图标列表（根据分类数量动态生成）
     */
    public static function getBuiltinIcons()
    {
        $categories = \app\common\model\PostCategory::getEnabledCategories();
        $icons = [];

        // 根据分类数量生成图标（每个分类对应一个图标）
        $iconIndex = 1;
        foreach ($categories as $category) {
            $iconKey = 'category_' . $category->id;
            $icons[$iconKey] = [
                'path' => '/images/icons/category_' . $iconIndex . '.png',
                'name' => $category->name,
                'category_id' => $category->id
            ];
            $iconIndex++;
        }

        return $icons;
    }

    // 保持向后兼容
    const BUILTIN_ICONS = [
        'sale' => '/images/icons/sale.png',
        'hot' => '/images/icons/hot.png',
        'expert' => '/images/icons/expert.png',
        'normal' => '/images/icons/normal.png',
        'new' => '/images/icons/new.png',
        'top' => '/images/icons/top.png'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo('PostCategory', 'category_id');
    }

    /**
     * 获取帖子类型文本
     */
    public function getPostTypeTextAttr($value, $data)
    {
        $postType = $data['post_type'] ?? 'normal';
        return self::POST_TYPES[$postType] ?? self::POST_TYPES['normal'];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::STATUS_LIST[$data['status']] ?? '未知';
    }

    /**
     * 获取图标URL
     */
    public function getIconUrlAttr($value, $data)
    {
        // 如果是上传的自定义图标
        if ($data['icon_type'] == 'upload' && $data['icon_path']) {
            return $data['icon_path'];
        }

        // 默认使用分类的图标
        if (isset($data['category_id']) && $data['category_id']) {
            $category = \app\common\model\PostCategory::get($data['category_id']);
            if ($category && $category->icon) {
                return $category->icon;
            }
        }

        // 如果分类没有图标，返回空（让前端处理）
        return '';
    }

    /**
     * 搜索器：按标题搜索
     */
    public function searchTitleAttr($query, $value)
    {
        $query->where('title', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器：按类型搜索
     */
    public function searchPostTypeAttr($query, $value)
    {
        $query->where('post_type', $value);
    }

    /**
     * 搜索器：按状态搜索
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 获取显示内容（处理收费内容逻辑）
     */
    public function getDisplayContentAttr($value, $data)
    {
        return $this->getDisplayContent();
    }

    /**
     * 获取显示内容（根据用户购买情况处理收费内容）
     * @param int|null $userId 用户ID
     * @return string
     */
    public function getDisplayContent($userId = null)
    {
        $content = $this->content;
        $paidIndex = $this->paid_content_index ?? 0;

        // 如果没有设置收费内容，直接返回原内容
        if ($paidIndex <= 0) {
            return $content;
        }

        // 按[SPLIT]分割内容
        $contents = explode('[SPLIT]', $content);

        // 检查收费段落索引是否有效
        if ($paidIndex > count($contents)) {
            return $content;
        }

        // 检查用户是否已购买
        $hasPurchased = false;
        if ($userId) {
            $hasPurchased = \app\common\model\PostPurchase::hasPurchased($userId, $this->id);
        }

        if (!$hasPurchased) {
            // 直接删除收费内容
            unset($contents[$paidIndex - 1]);
        }

        return implode('', $contents);
    }

    /**
     * 获取内容段落数组
     * @return array
     */
    public function getContentSegments()
    {
        return explode('[SPLIT]', $this->content);
    }

    /**
     * 检查是否有收费内容
     * @return bool
     */
    public function hasPaidContent()
    {
        return ($this->paid_content_index ?? 0) > 0;
    }

    /**
     * 增加浏览次数
     */
    public function incrementViews()
    {
        $this->setInc('views');
    }

    /**
     * 增加回复数
     */
    public function incrementReplies()
    {
        $this->setInc('replies');
    }

    /**
     * 增加点赞数
     */
    public function incrementLikes()
    {
        $this->setInc('likes');
    }
}
