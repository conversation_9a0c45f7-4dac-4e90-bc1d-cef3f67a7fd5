<?php

namespace app\common\model;

use think\Model;

/**
 * 帖子模型
 */
class Post extends Model
{

    // 表名
    protected $name = 'posts';

    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'post_type_text',
        'status_text',
        'icon_url',
        'display_content'
    ];

    // 帖子类型
    const POST_TYPES = [
        'normal' => '普通帖',
        'sale' => '出售帖',
        'hot' => '热门帖',
        'expert' => '高手帖'
    ];

    // 帖子状态
    const STATUS_LIST = [
        'draft' => '草稿',
        'published' => '已发布',
        'hidden' => '隐藏',
        'deleted' => '已删除'
    ];

    /**
     * 获取内置图标列表（根据分类数量动态生成）
     */
    public static function getBuiltinIcons()
    {
        $categories = \app\common\model\PostCategory::getEnabledCategories();
        $icons = [];

        // 根据分类数量生成图标（每个分类对应一个图标）
        $iconIndex = 1;
        foreach ($categories as $category) {
            $iconKey = 'category_' . $category->id;
            $icons[$iconKey] = [
                'path' => '/images/icons/category_' . $iconIndex . '.png',
                'name' => $category->name,
                'category_id' => $category->id
            ];
            $iconIndex++;
        }

        return $icons;
    }

    // 保持向后兼容
    const BUILTIN_ICONS = [
        'sale' => '/images/icons/sale.png',
        'hot' => '/images/icons/hot.png',
        'expert' => '/images/icons/expert.png',
        'normal' => '/images/icons/normal.png',
        'new' => '/images/icons/new.png',
        'top' => '/images/icons/top.png'
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo('PostCategory', 'category_id');
    }

    /**
     * 获取帖子类型文本
     */
    public function getPostTypeTextAttr($value, $data)
    {
        $postType = $data['post_type'] ?? 'normal';
        return self::POST_TYPES[$postType] ?? self::POST_TYPES['normal'];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::STATUS_LIST[$data['status']] ?? '未知';
    }

    /**
     * 获取图标URL
     */
    public function getIconUrlAttr($value, $data)
    {
        // 如果是上传的自定义图标
        if ($data['icon_type'] == 'upload' && $data['icon_path']) {
            return $data['icon_path'];
        }

        // 默认使用分类的图标
        if (isset($data['category_id']) && $data['category_id']) {
            $category = \app\common\model\PostCategory::get($data['category_id']);
            if ($category && $category->icon) {
                return $category->icon;
            }
        }

        // 如果分类没有图标，返回空（让前端处理）
        return '';
    }

    /**
     * 搜索器：按标题搜索
     */
    public function searchTitleAttr($query, $value)
    {
        $query->where('title', 'like', '%' . $value . '%');
    }

    /**
     * 搜索器：按类型搜索
     */
    public function searchPostTypeAttr($query, $value)
    {
        $query->where('post_type', $value);
    }

    /**
     * 搜索器：按状态搜索
     */
    public function searchStatusAttr($query, $value)
    {
        $query->where('status', $value);
    }

    /**
     * 获取显示内容（处理收费内容逻辑）
     */
    public function getDisplayContentAttr($value, $data)
    {
        return $this->getDisplayContent();
    }

    /**
     * 获取显示内容（根据用户购买情况处理收费内容）
     * @param int|null $userId 用户ID
     * @return string
     */
    public function getDisplayContent($userId = null)
    {
        $content = $this->content;

        // 按[SPLIT]分割内容
        $segments = explode('[SPLIT]', $content);

        $displaySegments = [];

        foreach ($segments as $index => $segment) {
            // 跳过空段落
            if (trim($segment) === '') {
                continue;
            }

            $segmentIndex = $index + 1; // 段落索引从1开始

            // 判断是否为收费段落：奇数索引为免费，偶数索引为收费
            // 例如：免费[SPLIT]收费[SPLIT]免费[SPLIT]收费[SPLIT]免费
            // 索引：  1        2      3        4      5
            $isPaidSegment = ($index % 2 === 1); // 偶数位置（索引1,3,5...）是收费内容

            if ($isPaidSegment) {
                // 这是收费段落，检查用户是否已购买
                $hasPurchased = false;
                if ($userId) {
                    $hasPurchased = \app\common\model\PostPurchase::hasPurchasedSegment($userId, $this->id, $segmentIndex);
                }

                if ($hasPurchased) {
                    // 已购买，显示完整内容
                    $displaySegments[] = $segment;
                } else {
                    // 未购买，跳过收费内容
                    continue;
                }
            } else {
                // 免费段落，直接显示
                $displaySegments[] = $segment;
            }
        }

        return implode('', $displaySegments);
    }

    /**
     * 获取收费段落信息
     * @param int|null $userId 用户ID
     * @return array
     */
    public function getPaidSegmentsInfo($userId = null)
    {
        $content = $this->content;
        $segments = explode('[SPLIT]', $content);
        $paidSegments = [];

        foreach ($segments as $index => $segment) {
            // 跳过空段落
            if (trim($segment) === '') {
                continue;
            }

            $segmentIndex = $index + 1;

            // 判断是否为收费段落
            $isPaidSegment = ($index % 2 === 1);

            if ($isPaidSegment) {
                // 检查是否已购买
                $hasPurchased = false;
                if ($userId) {
                    $hasPurchased = \app\common\model\PostPurchase::hasPurchasedSegment($userId, $this->id, $segmentIndex);
                }

                $paidSegments[] = [
                    'segment_index' => $segmentIndex,
                    'price' => $this->paid_content_price ?: 100, // 默认价格100金币
                    'title' => '收费内容 ' . (count($paidSegments) + 1),
                    'has_purchased' => $hasPurchased,
                    'preview' => mb_substr(strip_tags($segment), 0, 50) . '...'
                ];
            }
        }

        return $paidSegments;
    }

    /**
     * 检查是否有收费内容
     * @return bool
     */
    public function hasPaidContent()
    {
        return strpos($this->content, '[SPLIT]') !== false;
    }

    /**
     * 获取内容段落数组
     * @return array
     */
    public function getContentSegments()
    {
        return explode('[SPLIT]', $this->content);
    }



    /**
     * 增加浏览次数
     */
    public function incrementViews()
    {
        $this->setInc('views');
    }

    /**
     * 增加回复数
     */
    public function incrementReplies()
    {
        $this->setInc('replies');
    }

    /**
     * 增加点赞数
     */
    public function incrementLikes()
    {
        $this->setInc('likes');
    }
}
