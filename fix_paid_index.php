<?php
// 修复收费段落索引的脚本

// 数据库配置
$host = '127.0.0.1';
$dbname = 'houtai';
$username = 'root';
$password = '661381abc';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== 修复收费段落索引 ===\n";
    
    // 更新帖子ID为5的收费段落索引
    $stmt = $pdo->prepare("UPDATE fa_posts SET paid_content_index = 2 WHERE id = 5");
    $result = $stmt->execute();
    
    if ($result) {
        echo "✅ 已将帖子ID为5的收费段落索引从7改为2\n";
        
        // 验证修改结果
        $stmt = $pdo->prepare("SELECT paid_content_index, paid_content_price FROM fa_posts WHERE id = 5");
        $stmt->execute();
        $post = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "✅ 当前设置：收费段落索引={$post['paid_content_index']}, 价格={$post['paid_content_price']}金币\n";
        echo "✅ 现在第2段落（182期:【安居乐业】🏕⒍码中特🏕）将需要购买才能查看\n";
        
    } else {
        echo "❌ 更新失败\n";
    }
    
} catch (PDOException $e) {
    echo "❌ 数据库操作失败: " . $e->getMessage() . "\n";
}
