-- 检查和修复购买记录

-- 1. 查看帖子ID为5的所有购买记录
SELECT 
    id,
    user_id,
    post_id,
    segment_index,
    price,
    status,
    FROM_UNIXTIME(createtime) as purchase_time
FROM fa_post_purchases 
WHERE post_id = 5;

-- 2. 查看帖子ID为5的配置
SELECT 
    id,
    title,
    paid_content_index,
    paid_content_price
FROM fa_posts 
WHERE id = 5;

-- 3. 修复购买记录：将segment_index从0或其他值改为2（因为收费段落是第2段）
UPDATE fa_post_purchases 
SET segment_index = 2 
WHERE post_id = 5 AND segment_index != 2;

-- 4. 验证修复结果
SELECT 
    id,
    user_id,
    post_id,
    segment_index,
    price,
    status,
    FROM_UNIXTIME(createtime) as purchase_time
FROM fa_post_purchases 
WHERE post_id = 5;

-- 5. 检查用户2是否有购买记录
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ 用户2已购买帖子5的段落2'
        ELSE '❌ 用户2未购买帖子5的段落2'
    END as purchase_status
FROM fa_post_purchases 
WHERE user_id = 2 AND post_id = 5 AND segment_index = 2 AND status = 1;
