<?php
// 检查购买记录的脚本

require_once 'thinkphp/start.php';

use think\Db;
use app\common\model\PostPurchase;
use app\common\model\User;

try {
    // 检查用户ID为2的购买记录
    $purchases = PostPurchase::where('user_id', 2)->select();
    
    echo "用户ID为2的购买记录：\n";
    foreach ($purchases as $purchase) {
        echo "购买ID: " . $purchase->id . "\n";
        echo "帖子ID: " . $purchase->post_id . "\n";
        echo "价格: " . $purchase->price . "\n";
        echo "状态: " . $purchase->status . "\n";
        echo "购买时间: " . date('Y-m-d H:i:s', $purchase->createtime) . "\n";
        echo "---\n";
    }
    
    // 检查用户积分
    $user = User::where('id', 2)->find();
    if ($user) {
        echo "用户ID为2的当前积分: " . $user->score . "\n";
    }
    
    // 检查帖子ID为5的购买记录
    $postPurchases = PostPurchase::where('post_id', 5)->select();
    echo "\n帖子ID为5的所有购买记录：\n";
    foreach ($postPurchases as $purchase) {
        echo "用户ID: " . $purchase->user_id . " 价格: " . $purchase->price . " 时间: " . date('Y-m-d H:i:s', $purchase->createtime) . "\n";
    }
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
}
