<?php
// 调试帖子数据的脚本

require_once 'thinkphp/start.php';

use think\Db;
use app\common\model\Post;

try {
    // 查询帖子ID为5的数据
    $post = Post::where('id', 5)->find();
    
    if ($post) {
        echo "帖子信息：\n";
        echo "ID: " . $post->id . "\n";
        echo "标题: " . $post->title . "\n";
        echo "paid_content_index: " . ($post->paid_content_index ?? 'NULL') . "\n";
        echo "paid_content_price: " . ($post->paid_content_price ?? 'NULL') . "\n";
        echo "hasPaidContent(): " . ($post->hasPaidContent() ? 'true' : 'false') . "\n";
        echo "\n完整数据：\n";
        print_r($post->toArray());
    } else {
        echo "帖子ID为5的记录不存在\n";
    }
    
    // 检查表结构
    echo "\n\nfa_posts表结构：\n";
    $columns = Db::query("SHOW COLUMNS FROM fa_posts");
    foreach ($columns as $column) {
        if (strpos($column['Field'], 'paid') !== false) {
            echo $column['Field'] . " - " . $column['Type'] . " - " . $column['Default'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
}
