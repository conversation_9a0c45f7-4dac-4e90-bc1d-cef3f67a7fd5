<?php
// 分析帖子内容结构的脚本

require_once 'thinkphp/start.php';

use app\common\model\Post;

try {
    $postId = 5;
    $post = Post::where('id', $postId)->find();
    
    if ($post) {
        echo "=== 帖子内容分析 ===\n";
        echo "帖子ID: " . $post->id . "\n";
        echo "标题: " . $post->title . "\n";
        echo "当前paid_content_index: " . $post->paid_content_index . "\n";
        echo "当前paid_content_price: " . $post->paid_content_price . "\n\n";
        
        // 分析内容结构
        $content = $post->content;
        $segments = explode('[SPLIT]', $content);
        
        echo "=== 内容段落分析 ===\n";
        echo "总段落数: " . count($segments) . "\n\n";
        
        for ($i = 0; $i < count($segments); $i++) {
            $segmentText = strip_tags($segments[$i]);
            $preview = substr($segmentText, 0, 100);
            echo "段落 " . ($i + 1) . ":\n";
            echo "  长度: " . strlen($segmentText) . " 字符\n";
            echo "  预览: " . $preview . "...\n";
            echo "  是否为当前收费段落: " . (($i + 1) == $post->paid_content_index ? 'YES' : 'NO') . "\n\n";
        }
        
        echo "=== 建议的改进方案 ===\n";
        echo "1. 修改数据库结构，支持多段落收费\n";
        echo "2. 每个段落可以设置不同的价格\n";
        echo "3. 购买记录需要记录具体的段落索引\n";
        echo "4. 前端需要为每个收费段落显示单独的购买按钮\n";
        
    } else {
        echo "帖子不存在\n";
    }
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
}
