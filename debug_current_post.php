<?php
// 调试当前帖子设置的脚本

define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

use app\common\model\Post;

try {
    $postId = 5;
    $post = Post::where('id', $postId)->find();
    
    if ($post) {
        echo "=== 当前帖子设置 ===\n";
        echo "帖子ID: " . $post->id . "\n";
        echo "标题: " . $post->title . "\n";
        echo "paid_content_index: " . ($post->paid_content_index ?? 'NULL') . "\n";
        echo "paid_content_price: " . ($post->paid_content_price ?? 'NULL') . "\n";
        echo "paid_segments_config: " . ($post->paid_segments_config ?? 'NULL') . "\n\n";
        
        echo "=== 内容分析 ===\n";
        $content = $post->content;
        echo "原始内容: " . $content . "\n\n";
        
        $segments = explode('[SPLIT]', $content);
        echo "分割后的段落数: " . count($segments) . "\n";
        
        for ($i = 0; $i < count($segments); $i++) {
            echo "段落 " . ($i + 1) . ": '" . $segments[$i] . "'\n";
        }
        
        echo "\n=== 收费配置分析 ===\n";
        $paidConfig = $post->getPaidSegmentsConfig();
        echo "收费配置: " . json_encode($paidConfig, JSON_UNESCAPED_UNICODE) . "\n";
        
        echo "\n=== 显示内容测试 ===\n";
        $displayContent = $post->getDisplayContent(null); // 未登录用户
        echo "未登录用户看到的内容:\n" . $displayContent . "\n";
        
    } else {
        echo "帖子不存在\n";
    }
    
} catch (Exception $e) {
    echo "错误：" . $e->getMessage() . "\n";
    echo "堆栈：" . $e->getTraceAsString() . "\n";
}
